{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-KAP4c6889lK2rKNaZTrYvdbxwAlTaFFEaGAdzGSKEvjPBI9x\"\n", "os.environ[\"OPENAI_API_BASE\"] = \"http://127.0.0.1:3000/v1\"\n", "\n", "os.environ[\"LANGSMITH_API_KEY\"] = \"***************************************************\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Load environment variables and set up auto-reload\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"attachments": {"572e3be3-d2fb-41a3-af44-351baca997c0.webp": {"image/webp": "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"}, "6b1de3cc-2d28-48ed-8f02-56b4a0a749e0.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["# User Clarification and Brief Generation\n", "\n", "*The goal of scoping is to gather user-context needed for research.*\n", "\n", "Here is our overall research flow:\n", "\n", "![image.png](attachment:6b1de3cc-2d28-48ed-8f02-56b4a0a749e0.png)\n", "\n", "We'll scope the research in two phases:\n", "\n", "1. **User Clarification** - Determines if additional clarification is needed from the user\n", "2. **Brief Generation** - Transforms the conversation into a detailed research brief\n", "\n", "![image.webp](attachment:572e3be3-d2fb-41a3-af44-351baca997c0.webp)"]}, {"attachments": {"9f6f7dd6-215b-4183-8b85-3ccb5586f6ee.webp": {"image/webp": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["### Prompts\n", "\n", "A common challenge in deep research workflows is that users rarely provide sufficient context in their initial request. \n", "\n", "Requests often lack important details like:\n", "\n", "- **Scope and boundaries**: What should be included or excluded?\n", "- **Audience and purpose**: Who is this research for and why?\n", "- **Specific requirements**: Are there particular sources, timeframes, or constraints?\n", "- **Clarification of terms**: What do domain-specific terms or acronyms mean?\n", "\n", "Rather than making assumptions, we gather additional context through targeted clarification questions. \n", "\n", "This ensures we understand the user's true intent before investing time in research that might miss the mark.\n", "\n", "![image (1).webp](attachment:9f6f7dd6-215b-4183-8b85-3ccb5586f6ee.webp)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">与用户说明澄清事项</span><span style=\"color: #000080; text-decoration-color: #000080\"> ───────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  这些是用户迄今为止请求报告时交换的信息：                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Messages&gt;</span>                                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  {messages}                                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Messages&gt;</span>                                                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  今天的日期是 {date}。                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  请评估是否需要提出澄清问题，或者用户是否已经提供了足够的信息以开始研究。                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  重要提示：如果您在消息历史中看到您已经提出过澄清问题，几乎总是不需要再问另一个问题。只有在绝对必要时才提出另   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  一个问题。                                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  如果出现缩略词、缩写或未知术语，请请求用户澄清。                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  如果需要提问，请遵循以下指南：                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简明扼要地收集所有必要信息                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 确保以简洁、结构良好的方式收集完成研究任务所需的所有信息                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 如有必要，使用项目符号或编号列表以增强清晰度。确保使用 markdown 格式，并能在字符串输出传递给 markdown        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  渲染器时正确呈现                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 不要询问不必要的信息，或用户已提供过的信息。如果您看到用户已提供该信息，则不再重复提问                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  请以有效的 JSON 格式回复，包含以下确切键：                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  \"need_clarification\": boolean,                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  \"question\": \"<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;用于澄清报告范围的问题&gt;</span>\",                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  \"verification\": \"<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;我们将开始研究的确认信息&gt;</span>\"                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  如果需要提出澄清问题，返回：                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  \"need_clarification\": true,                                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  \"question\": \"<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;您的澄清问题&gt;</span>\",                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  \"verification\": \"\"                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  如果不需要澄清问题，返回：                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  \"need_clarification\": false,                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  \"question\": \"\",                                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  \"verification\": \"<span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;确认信息，说明您将根据所提供信息开始研究&gt;</span>\"                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  当不需要澄清时的确认信息应包括：                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 确认您已有足够信息继续                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简要总结您对用户请求的关键理解                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 确认您将开始研究过程                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 保持信息简洁且专业                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m─────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32m与用户说明澄清事项\u001b[0m\u001b[34m \u001b[0m\u001b[34m──────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  这些是用户迄今为止请求报告时交换的信息：                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Messages>\u001b[0m                                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  {messages}                                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Messages>\u001b[0m                                                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  今天的日期是 {date}。                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  请评估是否需要提出澄清问题，或者用户是否已经提供了足够的信息以开始研究。                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  重要提示：如果您在消息历史中看到您已经提出过澄清问题，几乎总是不需要再问另一个问题。只有在绝对必要时才提出另   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  一个问题。                                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  如果出现缩略词、缩写或未知术语，请请求用户澄清。                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  如果需要提问，请遵循以下指南：                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简明扼要地收集所有必要信息                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 确保以简洁、结构良好的方式收集完成研究任务所需的所有信息                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 如有必要，使用项目符号或编号列表以增强清晰度。确保使用 markdown 格式，并能在字符串输出传递给 markdown        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  渲染器时正确呈现                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 不要询问不必要的信息，或用户已提供过的信息。如果您看到用户已提供该信息，则不再重复提问                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  请以有效的 JSON 格式回复，包含以下确切键：                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \"need_clarification\": boolean,                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \"question\": \"\u001b[1;34m<用于澄清报告范围的问题>\u001b[0m\",                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \"verification\": \"\u001b[1;34m<我们将开始研究的确认信息>\u001b[0m\"                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  如果需要提出澄清问题，返回：                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \"need_clarification\": true,                                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \"question\": \"\u001b[1;34m<您的澄清问题>\u001b[0m\",                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \"verification\": \"\"                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  如果不需要澄清问题，返回：                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \"need_clarification\": false,                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \"question\": \"\",                                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \"verification\": \"\u001b[1;34m<确认信息，说明您将根据所提供信息开始研究>\u001b[0m\"                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  当不需要澄清时的确认信息应包括：                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 确认您已有足够信息继续                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简要总结您对用户请求的关键理解                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 确认您将开始研究过程                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 保持信息简洁且专业                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from utils import show_prompt\n", "from deep_research_from_scratch.prompts import clarify_with_user_instructions\n", "show_prompt(clarify_with_user_instructions, \"与用户说明澄清事项\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### State and Schemas\n", "\n", "First, we'll define the state object and schemas for our research process. \n", "\n", "The state object serves as our primary mechanism for storing and passing context between different phases of the research workflow. \n", "\n", "We can use it to [write and select context](https://blog.langchain.com/context-engineering-for-agents/) that will be used to guide the research.\n", " \n", "> **Note:** We will use `%%writefile` to save the code block to the specific file, `state_scope.py`. This allows us easily re-use it in future notebooks. And, it creates code that can used directly in a deployable LangGraph application!"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["\"\"\"State Definitions and Pydantic Schemas for Research Scoping.\n", "\n", "This defines the state objects and structured schemas used for\n", "the research agent scoping workflow, including researcher state management and output schemas.\n", "\"\"\"\n", "\n", "import operator\n", "from typing_extensions import Optional, Annotated, List, Sequence\n", "\n", "from langchain_core.messages import BaseMessage\n", "from langgraph.graph import MessagesState\n", "from langgraph.graph.message import add_messages\n", "from pydantic import BaseModel, Field\n", "\n", "# ===== STATE DEFINITIONS =====\n", "\n", "class AgentInputState(MessagesState):\n", "    \"\"\"Input state for the full agent - only contains messages from user input.\"\"\"\n", "    pass\n", "\n", "class AgentState(MessagesState):\n", "    \"\"\"\n", "    Main state for the full multi-agent research system.\n", "    \n", "    Extends MessagesState with additional fields for research coordination.\n", "    Note: Some fields are duplicated across different state classes for proper\n", "    state management between subgraphs and the main workflow.\n", "    \"\"\"\n", "\n", "    # Research brief generated from user conversation history\n", "    research_brief: Optional[str]\n", "    # Messages exchanged with the supervisor agent for coordination\n", "    supervisor_messages: Annotated[Sequence[BaseMessage], add_messages]\n", "    # Raw unprocessed research notes collected during the research phase\n", "    raw_notes: Annotated[list[str], operator.add] = []\n", "    # Processed and structured notes ready for report generation\n", "    notes: Annotated[list[str], operator.add] = []\n", "    # Final formatted research report\n", "    final_report: str\n", "\n", "# ===== STRUCTURED OUTPUT SCHEMAS =====\n", "\n", "class ClarifyWithUser(BaseModel):\n", "    \"\"\"Schema for user clarification decision and questions.\"\"\"\n", "    \n", "    need_clarification: bool = Field(\n", "        description=\"Whether the user needs to be asked a clarifying question.\",\n", "    )\n", "    question: str = Field(\n", "        description=\"A question to ask the user to clarify the report scope\",\n", "    )\n", "    verification: str = Field(\n", "        description=\"Verify message that we will start research after the user has provided the necessary information.\",\n", "    )\n", "\n", "class ResearchQuestion(BaseModel):\n", "    \"\"\"Schema for structured research brief generation.\"\"\"\n", "    \n", "    research_brief: str = Field(\n", "        description=\"A research question that will be used to guide the research.\",\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Scope Research\n", "\n", "Now, we'll create a simple workflow to clarify the user's intent and write a research brief.\n", "\n", "We'll let the LLM determine whether it has sufficient clarification to write the brief.\n", " \n", "This will use LangGraph's [Command](https://langchain-ai.github.io/langgraph/how-tos/graph-api/#combine-control-flow-and-state-updates-with-command) to direct the control flow and updating state. The `Command` object takes two key parameters:\n", "- `goto`: Specifies the next node to execute (or `END` to terminate)\n", "- `update`: Dictionary of state updates to apply before transitioning\n", "\n", "This pattern allows our functions to both process data and direct the workflow based on their results. \n", "\n", "It creates a more flexible and maintainable system than traditional static graph structures."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["\"\"\"User Clarification and Research Brief Generation.\n", "\n", "This module implements the scoping phase of the research workflow, where we:\n", "1. <PERSON><PERSON><PERSON> if the user's request needs clarification\n", "2. Generate a detailed research brief from the conversation\n", "\n", "The workflow uses structured output to make deterministic decisions about\n", "whether sufficient context exists to proceed with research.\n", "\"\"\"\n", "\n", "from datetime import datetime\n", "from typing_extensions import Literal\n", "\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.messages import HumanMessage, AIMessage, get_buffer_string\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.types import Command\n", "\n", "from deep_research_from_scratch.prompts import clarify_with_user_instructions, transform_messages_into_research_topic_prompt\n", "from deep_research_from_scratch.state_scope import AgentState, ClarifyWithUser, ResearchQuestion, AgentInputState\n", "\n", "# ===== UTILITY FUNCTIONS =====\n", "\n", "def get_today_str() -> str:\n", "    \"\"\"Get current date in a human-readable format.\"\"\"\n", "    return datetime.now().strftime(\"%a %b %-d, %Y\")\n", "\n", "# ===== CONFIGURATION =====\n", "\n", "# Initialize model\n", "model = init_chat_model(\n", "    model=\"openai:gpt-4.1-nano\", \n", "    temperature=0.0,\n", "    # model_kwargs={\n", "    #     \"base_url\": \"http://127.0.0.1:3000/v1\",\n", "    #     \"api_key\": \"sk-Tm3hYpGyEv7SxPwO4sFipXWRHM8FY2lkb2xYKFl0ypxz86NT\",\n", "    # }\n", ")\n", "\n", "# ===== WORKFLOW NODES =====\n", "\n", "def clarify_with_user(state: AgentState) -> Command[Literal[\"write_research_brief\", \"__end__\"]]:\n", "    \"\"\"\n", "    Determine if the user's request contains sufficient information to proceed with research.\n", "    \n", "    Uses structured output to make deterministic decisions and avoid hallucination.\n", "    Routes to either research brief generation or ends with a clarification question.\n", "    \"\"\"\n", "    # Set up structured output model\n", "    structured_output_model = model.with_structured_output(ClarifyWithUser)\n", "\n", "    # Invoke the model with clarification instructions\n", "    response = structured_output_model.invoke([\n", "        HumanMessage(content=clarify_with_user_instructions.format(\n", "            messages=get_buffer_string(messages=state[\"messages\"]), \n", "            date=get_today_str()\n", "        ))\n", "    ])\n", "    \n", "    # Route based on clarification need\n", "    if response.need_clarification:\n", "        return Command(\n", "            goto=END, \n", "            update={\"messages\": [AIMessage(content=response.question)]}\n", "        )\n", "    else:\n", "        return Command(\n", "            goto=\"write_research_brief\", \n", "            update={\"messages\": [AIMessage(content=response.verification)]}\n", "        )\n", "\n", "def write_research_brief(state: AgentState):\n", "    \"\"\"\n", "    Transform the conversation history into a comprehensive research brief.\n", "    \n", "    Uses structured output to ensure the brief follows the required format\n", "    and contains all necessary details for effective research.\n", "    \"\"\"\n", "    # Set up structured output model\n", "    structured_output_model = model.with_structured_output(ResearchQuestion)\n", "    \n", "    # Generate research brief from conversation history\n", "    response = structured_output_model.invoke([\n", "        HumanMessage(content=transform_messages_into_research_topic_prompt.format(\n", "            messages=get_buffer_string(state.get(\"messages\", [])),\n", "            date=get_today_str()\n", "        ))\n", "    ])\n", "    \n", "    # Update state with generated research brief and pass it to the supervisor\n", "    return {\n", "        \"research_brief\": response.research_brief,\n", "        \"supervisor_messages\": [HumanMessage(content=f\"{response.research_brief}.\")]\n", "    }\n", "\n", "# ===== GRAPH CONSTRUCTION =====\n", "\n", "# Build the scoping workflow\n", "deep_researcher_builder = StateGraph(AgentState, input_schema=AgentInputState)\n", "\n", "# Add workflow nodes\n", "deep_researcher_builder.add_node(\"clarify_with_user\", clarify_with_user)\n", "deep_researcher_builder.add_node(\"write_research_brief\", write_research_brief)\n", "\n", "# Add workflow edges\n", "deep_researcher_builder.add_edge(START, \"clarify_with_user\")\n", "deep_researcher_builder.add_edge(\"write_research_brief\", END)\n", "\n", "# Compile the workflow\n", "scope_research = deep_researcher_builder.compile()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Compile with in-memory checkpointer to test in notebook\n", "from IPython.display import Image, display\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "from deep_research_from_scratch.research_agent_scope import deep_researcher_builder\n", "\n", "checkpointer = InMemorySaver()\n", "scope = deep_researcher_builder.compile(checkpointer=checkpointer)\n", "display(Image(scope.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭─────────────────────────────────────────────────── 📝 System ───────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 使用中文回答问题                                                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m──────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 System \u001b[0m\u001b[37m──────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m 使用中文回答问题                                                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 我想研究上海最好的咖啡店。                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m 我想研究上海最好的咖啡店。                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 我已拥有足够信息，确认用户希望研究上海最好的咖啡店，我将开始进行相关研究。                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m 我已拥有足够信息，确认用户希望研究上海最好的咖啡店，我将开始进行相关研究。                                      \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Run the workflow\n", "from utils import format_messages\n", "from langchain_core.messages import HumanMessage,SystemMessage\n", "thread = {\"configurable\": {\"thread_id\": \"1\"}}\n", "result = scope.invoke(\n", "    {\"messages\": [\n", "            SystemMessage(content=\"使用中文回答问题\"),\n", "            HumanMessage(content=\"我想研究上海最好的咖啡店。\")\n", "        ]\n", "    }, \n", "    config=thread\n", ")\n", "format_messages(result['messages'])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭─────────────────────────────────────────────────── 📝 System ───────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 使用中文回答问题                                                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m──────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 System \u001b[0m\u001b[37m──────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m 使用中文回答问题                                                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 我想研究上海最好的咖啡店。                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m 我想研究上海最好的咖啡店。                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 我已拥有足够信息，确认用户希望研究上海最好的咖啡店，我将开始进行相关研究。                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m 我已拥有足够信息，确认用户希望研究上海最好的咖啡店，我将开始进行相关研究。                                      \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> 我要关注咖啡的质量                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m 我要关注咖啡的质量                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 我已有足够信息继续。用户希望关注上海最好的咖啡店，特别是关于咖啡的质量。我将开始进行相关研究。                  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m 我已有足够信息继续。用户希望关注上海最好的咖啡店，特别是关于咖啡的质量。我将开始进行相关研究。                  \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["result = scope.invoke(\n", "    {\"messages\": [HumanMessage(content=\"我要关注咖啡的质量\")]}, \n", "    config=thread\n", ")\n", "format_messages(result['messages'])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">我希望研究上海地区的咖啡店，特别关注其咖啡的质量。请提供一份详细且具体的研究问题，涵盖所有已知偏好，包括对咖啡质量 \n", "的关注，并明确考虑可能影响咖啡质量的关键因素，如咖啡豆来源、烘焙程度、冲泡技术等。研究应优先参考官方或信誉良好的资 \n", "料来源，如咖啡店官方网站、专业咖啡评测网站和行业报告。                                                             \n", "</pre>\n"], "text/plain": ["我希望研究上海地区的咖啡店，特别关注其咖啡的质量。请提供一份详细且具体的研究问题，涵盖所有已知偏好，包括对咖啡质量 \n", "的关注，并明确考虑可能影响咖啡质量的关键因素，如咖啡豆来源、烘焙程度、冲泡技术等。研究应优先参考官方或信誉良好的资 \n", "料来源，如咖啡店官方网站、专业咖啡评测网站和行业报告。                                                             \n"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from rich.markdown import Markdown\n", "Markdown(result[\"research_brief\"])"]}, {"cell_type": "markdown", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["We can look at the [trace](https://smith.langchain.com/public/75278fdf-4468-4dcc-bf44-a28ab6018d92/r).\n", "\n", "### Local Deployment and LangGraph Studio\n", "\n", "LangGraph is designed to seamlessly support deployment. \n", "\n", "The files we wrote with `%%writefile` to `src/deep_research_from_scratch/` during this notebook form the basis of our application:\n", "\n", "```\n", "deep_research_from_scratch/\n", "├── src/deep_research_from_scratch/\n", "│   ├── state.py          # State definitions\n", "│   ├── scope_research.py # Scoping workflow\n", "│   ├── prompts.py        # Prompt templates\n", "│   └── ...\n", "├── notebooks/            # Development notebooks\n", "├── pyproject.toml        # Dependencies\n", "└── langgraph.json        # LangGraph configuration\n", "```\n", "\n", "In addition, the repo has a `langgraph.json` file that specifies the dependencies, graphs, and environment variables.\n", "\n", "This structure allows the scoping workflow [to be deployed either locally or via a remote server](https://langchain-ai.github.io/langgraph/concepts/deployment_options/).\n", "\n", "We can kick off LangGraph server locally with the below command from the root of the repo, which will open LangGraph Studio in our browser:\n", "\n", "```bash\n", "uvx --refresh --from \"langgraph-cli[inmem]\" --with-editable . --python 3.11 langgraph dev\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluation\n", "\n", "Now that we've scoped our research, let's test it with a few examples to make sure that it's working as expected.\n", "\n", "Let's think about what makes for a good research brief:\n", "\n", "* It captures relevant criteria from the user chat\n", "* It does not invent or assume any criteria that the user did not explicitly provide\n", "\n", "Here are two sample input conversations."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import AIMessage\n", "\n", "conversation_1 = [\n", "    HumanMessage(content=\"投资5万美元用于退休的最佳方式是什么？\"),\n", "    AIMessage(content=\"您能否提供一些额外的信息，以便为您的50,000美元退休目标量身定制投资建议？具体包括：\\n- 您当前的年龄或期望的退休年龄\\n- 您的风险承受能力（低、中、高）\\n- 您对投资类型的偏好（例如，股票、债券、共同基金、房地产）\\n- 您是通过税优账户（例如，IRA、401(k)）还是普通经纪账户进行投资\\n这些信息将帮助我提供更个性化和相关的建议。\"),\n", "    HumanMessage(content=\"我今年25岁，计划在45岁退休。我目前的风险承受能力很高，但我认为随着时间的推移会降低。我听说股票和ETF是不错的选择，但我对任何投资都持开放态度。我已经有一个401k账户，但这次投资将通过普通的经纪账户进行。\"),\n", "]\n", "\n", "conversation_2 = [\n", "    HumanMessage(content=\"我正在纽约市寻找一套公寓，你能帮我吗？\"),\n", "    AIMessage(content=\"你能否具体说明你的公寓偏好？例如：\\n- 想要的社区或区\\n- 卧室/浴室数量\\n- 预算范围（月租）\\n- 任何设施或必备特点\\n- 首选入住日期\\n这些信息将帮助我提供最相关的纽约市公寓选项。\\n\"),\n", "    HumanMessage(content=\"我更喜欢住在切尔西、扁铁区或西村。我想找一套两室两卫的公寓，月租希望在7000美元以下。我希望这是一栋有门卫的建筑，并且有室内洗衣机和烘干机，但没有洗衣机和烘干机也可以。如果建筑有健身房那就更好了。我希望能在2025年9月搬进去。\"),\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's manually write out each criteria from these conversations that we would want preserved by a research brief."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["criteria_1 = [\n", "    \"当前年龄为25岁\",\n", "    \"期望退休年龄为45岁\",\n", "    \"当前风险承受能力高\",\n", "    \"有兴趣投资股票和ETF\",\n", "    \"对股票和ETF以外的投资形式持开放态度\",\n", "    \"投资账户为普通经纪账户\",\n", "]\n", "\n", "criteria_2 = [\n", "    \"寻找切尔西、扁钢区或西村的两室两卫公寓\",\n", "    \"月租低于7000美元\",\n", "    \"应位于有门卫的建筑中\",\n", "    \"理想情况下有套内洗衣机和干衣机，但不是硬性要求\",\n", "    \"理想情况下有健身房，但不是硬性要求\",\n", "    \"入住日期为2025年9月\"\n", "]"]}, {"attachments": {"838202f9-a45d-44c0-9af8-b824029376a8.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["We're going to use <PERSON><PERSON><PERSON> to run this experiment. \n", "\n", "> Note Make sure you follow the relevant instructions in the `README` to set up a LangSmith account (free tier is sufficient!) have `LANGSMITH_API_KEY` set in your environment. \n", "\n", "Running an experiment in Lang<PERSON><PERSON> comprises of three steps: \n", "\n", "1. Creating the dataset\n", "2. Writing the evaluator(s)\n", "3. Running the experiment\n", "\n", "The flow will look like this:\n", "\n", "![Screenshot 2025-07-29 at 3.31.55 PM.png](attachment:838202f9-a45d-44c0-9af8-b824029376a8.png)\n", "\n", "We'll start by creating our dataset and adding our two examples to it."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import os\n", "from langsmith import Client\n", "\n", "# Initialize the LangSmith client\n", "langsmith_client = Client(api_key=os.getenv(\"LANGSMITH_API_KEY\"))\n", "\n", "# Create the dataset\n", "dataset_name = \"deep_research_scoping\"\n", "if not langsmith_client.has_dataset(dataset_name=dataset_name):\n", "    \n", "    # Create the dataset\n", "    dataset = langsmith_client.create_dataset(\n", "        dataset_name=dataset_name,\n", "        description=\"A dataset that measures the quality of research briefs generated from an input conversation\",\n", "    )\n", "\n", "    # Add the examples to the dataset\n", "    langsmith_client.create_examples(\n", "        dataset_id=dataset.id,\n", "        examples=[\n", "            {\n", "                \"inputs\": {\"messages\": conversation_1},\n", "                \"outputs\": {\"criteria\": criteria_1},\n", "            },\n", "            {\n", "                \"inputs\": {\"messages\": conversation_2},\n", "                \"outputs\": {\"criteria\": criteria_2},\n", "            },\n", "        ],\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we need to write an evaluator that will compare our research brief against the success criteria that we have specified for each example. For this, we'll use an LLM-as-judge. You can fine some useful tips for writing llm-as-judge evaluators [here](https://hamel.dev/blog/posts/llm-judge/index.html), which include:\n", "\n", "1. **Role Definition with Expertise Context**\n", "   - Defined specific expert roles (\"research brief evaluator\", \"meticulous auditor\")  \n", "   - Specialized the role to the specific evaluation domain\n", "\n", "2. **Clear Task Specification**\n", "   - Binary pass/fail judgments (avoiding complex multi-dimensional scoring)\n", "   - Explicit task boundaries and objectives\n", "   - Focus on actionable evaluation criteria\n", "\n", "3. **Rich Contextual Background**\n", "   - Provide domain-specific context about research brief quality\n", "   - Explain the importance of accurate evaluation\n", "   - Connect evaluation outcomes to downstream consequences\n", "\n", "4. **Structured XML Organization**\n", "   - Used semantic XML tags for different sections\n", "   - Clear separation of role, task, context, inputs, guidelines, and outputs\n", "   - Improved prompt parsing and comprehension\n", "\n", "5. **Comprehensive Guidelines with Examples**\n", "   - Detailed PASS/FAIL criteria with specific conditions\n", "   - Multiple concrete examples showing correct judgments\n", "   - 3-4 examples per prompt covering different scenarios\n", "   - Both positive and negative examples for each judgment type\n", "   - Edge case handling and decision boundary clarification\n", "\n", "6. **Explicit Output Instructions**\n", "   - Clear guidance on how to apply the evaluation criteria\n", "   - Instructions for handling ambiguous cases\n", "   - Emphasis on consistency and systematic evaluation\n", "\n", "7. **Bias Reduction Techniques**\n", "   - \"Strict but fair\" guidance to balance precision and recall\n", "   - \"When in doubt, lean toward FAIL\" for conservative evaluation\n", "   - Systematic evaluation process to reduce subjective variation"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭───────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">简要标准提示</span><span style=\"color: #000080; text-decoration-color: #000080\"> ──────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;role&gt;</span>                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  您是一位资深的研究简报评估专家，专门评估生成的研究简报是否准确捕捉用户指定的标准且不遗漏重要细节。             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/role&gt;</span>                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;task&gt;</span>                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断研究简报是否充分涵盖所提供的具体成功标准。给出二元评估并附详细理由。                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/task&gt;</span>                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;evaluation_context&gt;</span>                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  研究简报对于指导后续研究代理至关重要。遗漏或未充分反映的标准可能导致研究不完整，无法满足用户需求。准确的评估   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  确保研究质量和用户满意度。                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/evaluation_context&gt;</span>                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;criterion_to_evaluate&gt;</span>                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  {criterion}                                                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/criterion_to_evaluate&gt;</span>                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;research_brief&gt;</span>                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  {research_brief}                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/research_brief&gt;</span>                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;evaluation_guidelines&gt;</span>                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  已涵盖（标准被充分体现）当且仅当：                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 研究简报明确提及或直接涉及该标准                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简报包含等效的语言或概念，清晰覆盖该标准                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 即使措辞不同，标准的意图得以保留                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 标准的所有关键方面均在简报中体现                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  未涵盖（标准缺失或未充分体现）当满足以下任一：                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 该标准在研究简报中完全缺失                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简报仅部分涉及标准，遗漏重要方面                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 标准仅被暗示，未明确陈述或研究人员无法据此采取行动                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简报与标准相矛盾或冲突                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;evaluation_examples&gt;</span>                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  示例1 - 已涵盖：                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  标准：“当前年龄为25岁”                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  简报：“……为一位25岁投资者提供投资建议……”                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断：已涵盖——年龄被明确提及                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  示例2 - 未涵盖：                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  标准：“月租低于7000”                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  简报：“……寻找曼哈顿带良好设施的公寓……”                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断：未涵盖——预算限制完全缺失                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  示例3 - 已涵盖：                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  标准：“高风险承受能力”                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  简报：“……愿意接受显著市场波动以追求更高收益……”                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断：已涵盖——等效概念以不同表达形式呈现                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  示例4 - 未涵盖：                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  标准：“要求有门卫的楼宇”                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  简报：“……寻找带现代设施的公寓……”                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断：未涵盖——未提及具体门卫要求                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/evaluation_examples&gt;</span>                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/evaluation_guidelines&gt;</span>                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;output_instructions&gt;</span>                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. 仔细检查研究简报中是否有该具体标准的证据                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. 查找明确提及和等效概念                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  3. 提供简报中的具体引用或依据作为证据                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  4. 系统性评估——若对部分覆盖存疑，为保证质量，应偏向判定为未涵盖                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  5. 关注研究人员是否能仅凭简报对该标准采取行动                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/output_instructions&gt;</span>                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32m简要标准提示\u001b[0m\u001b[34m \u001b[0m\u001b[34m─────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<role>\u001b[0m                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  您是一位资深的研究简报评估专家，专门评估生成的研究简报是否准确捕捉用户指定的标准且不遗漏重要细节。             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</role>\u001b[0m                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<task>\u001b[0m                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断研究简报是否充分涵盖所提供的具体成功标准。给出二元评估并附详细理由。                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</task>\u001b[0m                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<evaluation_context>\u001b[0m                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  研究简报对于指导后续研究代理至关重要。遗漏或未充分反映的标准可能导致研究不完整，无法满足用户需求。准确的评估   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  确保研究质量和用户满意度。                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</evaluation_context>\u001b[0m                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<criterion_to_evaluate>\u001b[0m                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  {criterion}                                                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</criterion_to_evaluate>\u001b[0m                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<research_brief>\u001b[0m                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  {research_brief}                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</research_brief>\u001b[0m                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<evaluation_guidelines>\u001b[0m                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  已涵盖（标准被充分体现）当且仅当：                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 研究简报明确提及或直接涉及该标准                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简报包含等效的语言或概念，清晰覆盖该标准                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 即使措辞不同，标准的意图得以保留                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 标准的所有关键方面均在简报中体现                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  未涵盖（标准缺失或未充分体现）当满足以下任一：                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 该标准在研究简报中完全缺失                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简报仅部分涉及标准，遗漏重要方面                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 标准仅被暗示，未明确陈述或研究人员无法据此采取行动                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简报与标准相矛盾或冲突                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<evaluation_examples>\u001b[0m                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  示例1 - 已涵盖：                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  标准：“当前年龄为25岁”                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  简报：“……为一位25岁投资者提供投资建议……”                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断：已涵盖——年龄被明确提及                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  示例2 - 未涵盖：                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  标准：“月租低于7000”                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  简报：“……寻找曼哈顿带良好设施的公寓……”                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断：未涵盖——预算限制完全缺失                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  示例3 - 已涵盖：                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  标准：“高风险承受能力”                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  简报：“……愿意接受显著市场波动以追求更高收益……”                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断：已涵盖——等效概念以不同表达形式呈现                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  示例4 - 未涵盖：                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  标准：“要求有门卫的楼宇”                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  简报：“……寻找带现代设施的公寓……”                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断：未涵盖——未提及具体门卫要求                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</evaluation_examples>\u001b[0m                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</evaluation_guidelines>\u001b[0m                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<output_instructions>\u001b[0m                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. 仔细检查研究简报中是否有该具体标准的证据                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. 查找明确提及和等效概念                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  3. 提供简报中的具体引用或依据作为证据                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  4. 系统性评估——若对部分覆盖存疑，为保证质量，应偏向判定为未涵盖                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  5. 关注研究人员是否能仅凭简报对该标准采取行动                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</output_instructions>\u001b[0m                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from deep_research_from_scratch.prompts import BRIEF_CRITERIA_PROMPT\n", "show_prompt(BRIEF_CRITERIA_PROMPT, \"简要标准提示\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from typing_extensions import cast\n", "from pydantic import BaseModel, Field\n", "from langchain_openai import ChatOpenAI\n", "\n", "class Criteria(BaseModel):\n", "    \"\"\"\n", "    Individual success criteria evaluation result.\n", "    \n", "    This model represents a single evaluation criteria that should be present\n", "    in the research brief, along with a detailed assessment of whether it was\n", "    successfully captured and the reasoning behind that assessment.\n", "    \"\"\"\n", "    criteria_text: str = Field(\n", "        description=\"The specific success criteria being evaluated (e.g., 'Current age is 25', 'Monthly rent below 7k')\"\n", "    )\n", "    reasoning: str = Field(\n", "        description=\"Detailed explanation of why this criteria is or isn't captured in the research brief, including specific evidence from the brief\"\n", "    )\n", "    is_captured: bool = Field(\n", "        description=\"Whether this specific criteria is adequately captured in the research brief (True) or missing/inadequately addressed (False)\"\n", "    )\n", "\n", "def evaluate_success_criteria(outputs: dict, reference_outputs: dict):\n", "    \"\"\"\n", "    Evaluate whether the research brief captures all required success criteria.\n", "    \n", "    This function evaluates each criterion individually to provide focused assessment\n", "    and detailed reasoning for each evaluation decision.\n", "    \n", "    Args:\n", "        outputs: Dictionary containing the research brief to evaluate\n", "        reference_outputs: Dictionary containing the list of success criteria\n", "        \n", "    Returns:\n", "        Dict with evaluation results including score (0.0 to 1.0)\n", "    \"\"\"\n", "    research_brief = outputs[\"research_brief\"]\n", "    success_criteria = reference_outputs[\"criteria\"]\n", "\n", "    model = ChatOpenAI(model=\"gpt-4.1-nano\", temperature=0)\n", "    structured_output_model = model.with_structured_output(Criteria)\n", "    \n", "    # Run evals\n", "    responses = structured_output_model.batch([\n", "    [\n", "        HumanMessage(\n", "            content=BRIEF_CRITERIA_PROMPT.format(\n", "                research_brief=research_brief,\n", "                criterion=criterion\n", "            )\n", "        )\n", "    ] \n", "    for criterion in success_criteria])\n", "    \n", "    # Ensure the criteria_text field is populated correctly\n", "    individual_evaluations = [\n", "        Criteria(\n", "            reasoning=response.reasoning,\n", "            criteria_text=criterion,\n", "            is_captured=response.is_captured\n", "        )\n", "        for criterion, response in zip(success_criteria, responses)\n", "    ]\n", "    \n", "    # Calculate overall score as percentage of captured criteria\n", "    captured_count = sum(1 for eval_result in individual_evaluations if eval_result.is_captured)\n", "    total_count = len(individual_evaluations)\n", "    \n", "    return {\n", "        \"key\": \"success_criteria_score\", \n", "        \"score\": captured_count / total_count if total_count > 0 else 0.0,\n", "        \"individual_evaluations\": [\n", "            {\n", "                \"criteria\": eval_result.criteria_text,\n", "                \"captured\": eval_result.is_captured,\n", "                \"reasoning\": eval_result.reasoning\n", "            }\n", "            for eval_result in individual_evaluations\n", "        ]\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Our second evaluator will check that the research brief does not make any assumptions that the user did not specify in the research brief."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">BRIEF_HALLUCINATION_PROMPT</span><span style=\"color: #000080; text-decoration-color: #000080\"> ───────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">## 简要幻觉评估器</span>                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;role&gt;</span>                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  您是一名细致的研究简报审计员，专门识别可能误导研究工作的无根据假设。                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/role&gt;</span>                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;task&gt;</span>                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断研究简报是否做出了超出用户明确提供内容的假设。返回二元的通过/不通过判断。                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/task&gt;</span>                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;evaluation_context&gt;</span>                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  研究简报应仅包含用户明确说明或清楚暗示的需求、偏好和限制。加入假设可能导致研究偏离用户的实际需求。             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/evaluation_context&gt;</span>                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;research_brief&gt;</span>                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  {research_brief}                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/research_brief&gt;</span>                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;success_criteria&gt;</span>                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  {success_criteria}                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/success_criteria&gt;</span>                                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;evaluation_guidelines&gt;</span>                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  通过（无无根据假设）条件：                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简报仅包含用户明确提出的需求                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 任何推断都明确标注或逻辑上必需                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 来源建议为一般性推荐，而非具体假设                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简报范围符合用户实际请求                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  不通过（包含无根据假设）条件：                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简报添加用户未提及的具体偏好                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简报假设未提供的人口、地理或上下文细节                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简报将范围缩小至超出用户声明的限制                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - 简报引入用户未指定的要求                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;evaluation_examples&gt;</span>                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  示例 1 - 通过：                                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  用户条件：[\"寻找咖啡店\", \"在旧金山\"]                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  简报：“……研究旧金山地区的咖啡店……”                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断：通过 - 保持在声明范围内                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  示例 2 - 不通过：                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  用户条件：[\"寻找咖啡店\", \"在旧金山\"]                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  简报：“……研究旧金山面向年轻专业人士的时尚咖啡店……”                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断：不通过 - 假设了“时尚”和“年轻专业人士”人群                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  示例 3 - 通过：                                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  用户条件：[\"预算低于3000美元\", \"两居室公寓\"]                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  简报：“……在3000美元预算内寻找两居室公寓，参考租赁网站和本地房源……”                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断：通过 - 来源建议适当，无偏好假设                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  示例 4 - 不通过：                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  用户条件：[\"预算低于3000美元\", \"两居室公寓\"]                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  简报：“……寻找3000美元以下、位于安全社区且有好学校的现代两居室公寓……”                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  判断：不通过 - 假设了“现代”、“安全”和“好学校”偏好                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/evaluation_examples&gt;</span>                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/evaluation_guidelines&gt;</span>                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;output_instructions&gt;</span>                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  仔细检查简报中任何用户未明确提供的细节。严格把关——如果不确定某项内容是否由用户指定，应倾向于不通过。           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/output_instructions&gt;</span>                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m─────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mBRIEF_HALLUCINATION_PROMPT\u001b[0m\u001b[34m \u001b[0m\u001b[34m──────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;35m## 简要幻觉评估器\u001b[0m                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<role>\u001b[0m                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  您是一名细致的研究简报审计员，专门识别可能误导研究工作的无根据假设。                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</role>\u001b[0m                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<task>\u001b[0m                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断研究简报是否做出了超出用户明确提供内容的假设。返回二元的通过/不通过判断。                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</task>\u001b[0m                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<evaluation_context>\u001b[0m                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  研究简报应仅包含用户明确说明或清楚暗示的需求、偏好和限制。加入假设可能导致研究偏离用户的实际需求。             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</evaluation_context>\u001b[0m                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<research_brief>\u001b[0m                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  {research_brief}                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</research_brief>\u001b[0m                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<success_criteria>\u001b[0m                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  {success_criteria}                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</success_criteria>\u001b[0m                                                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<evaluation_guidelines>\u001b[0m                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  通过（无无根据假设）条件：                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简报仅包含用户明确提出的需求                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 任何推断都明确标注或逻辑上必需                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 来源建议为一般性推荐，而非具体假设                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简报范围符合用户实际请求                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  不通过（包含无根据假设）条件：                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简报添加用户未提及的具体偏好                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简报假设未提供的人口、地理或上下文细节                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简报将范围缩小至超出用户声明的限制                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - 简报引入用户未指定的要求                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<evaluation_examples>\u001b[0m                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  示例 1 - 通过：                                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  用户条件：[\"寻找咖啡店\", \"在旧金山\"]                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  简报：“……研究旧金山地区的咖啡店……”                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断：通过 - 保持在声明范围内                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  示例 2 - 不通过：                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  用户条件：[\"寻找咖啡店\", \"在旧金山\"]                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  简报：“……研究旧金山面向年轻专业人士的时尚咖啡店……”                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断：不通过 - 假设了“时尚”和“年轻专业人士”人群                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  示例 3 - 通过：                                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  用户条件：[\"预算低于3000美元\", \"两居室公寓\"]                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  简报：“……在3000美元预算内寻找两居室公寓，参考租赁网站和本地房源……”                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断：通过 - 来源建议适当，无偏好假设                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  示例 4 - 不通过：                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  用户条件：[\"预算低于3000美元\", \"两居室公寓\"]                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  简报：“……寻找3000美元以下、位于安全社区且有好学校的现代两居室公寓……”                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  判断：不通过 - 假设了“现代”、“安全”和“好学校”偏好                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</evaluation_examples>\u001b[0m                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</evaluation_guidelines>\u001b[0m                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<output_instructions>\u001b[0m                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  仔细检查简报中任何用户未明确提供的细节。严格把关——如果不确定某项内容是否由用户指定，应倾向于不通过。           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</output_instructions>\u001b[0m                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from deep_research_from_scratch.prompts import BRIEF_HALLUCINATION_PROMPT\n", "show_prompt(BRIEF_HALLUCINATION_PROMPT, \"BRIEF_HALLUCINATION_PROMPT\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Improved NoAssumptions class with reasoning field and enhanced descriptions\n", "class NoAssumptions(BaseModel):\n", "    \"\"\"\n", "    Evaluation model for checking if research brief makes unwarranted assumptions.\n", "    \n", "    This model evaluates whether the research brief contains any assumptions,\n", "    inferences, or additions that were not explicitly stated by the user in their\n", "    original conversation. It provides detailed reasoning for the evaluation decision.\n", "    \"\"\"\n", "    no_assumptions: bool = Field(\n", "        description=\"Whether the research brief avoids making unwarranted assumptions. True if the brief only includes information explicitly provided by the user, False if it makes assumptions beyond what was stated.\"\n", "    )\n", "    reasoning: str = Field(\n", "        description=\"Detailed explanation of the evaluation decision, including specific examples of any assumptions found or confirmation that no assumptions were made beyond the user's explicit statements.\"\n", "    )\n", "\n", "def evaluate_no_assumptions(outputs: dict, reference_outputs: dict):\n", "    \"\"\"\n", "    Evaluate whether the research brief avoids making unwarranted assumptions.\n", "    \n", "    This evaluator checks that the research brief only includes information\n", "    and requirements that were explicitly provided by the user, without\n", "    making assumptions about unstated preferences or requirements.\n", "    \n", "    Args:\n", "        outputs: Dictionary containing the research brief to evaluate\n", "        reference_outputs: Dictionary containing the success criteria for reference\n", "        \n", "    Returns:\n", "        Dict with evaluation results including boolean score and detailed reasoning\n", "    \"\"\"\n", "    research_brief = outputs[\"research_brief\"]\n", "    success_criteria = reference_outputs[\"criteria\"]\n", "    \n", "    model = ChatOpenAI(model=\"gpt-4.1-nano\", temperature=0)\n", "    structured_output_model = model.with_structured_output(NoAssumptions)\n", "    \n", "    response = structured_output_model.invoke([\n", "        HumanMessage(content=BRIEF_HALLUCINATION_PROMPT.format(\n", "            research_brief=research_brief, \n", "            success_criteria=str(success_criteria)\n", "        ))\n", "    ])\n", "    \n", "    return {\n", "        \"key\": \"no_assumptions_score\", \n", "        \"score\": response.no_assumptions,\n", "        \"reasoning\": response.reasoning\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have our evaluators, we can run our experiment."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["View the evaluation results for experiment: 'Deep Research Scoping-6bf0b71c' at:\n", "https://smith.langchain.com/o/80ccd707-af06-47c6-9ade-4fd2f44f8cae/datasets/4675f6c5-932e-4e84-a3cc-3a23873f1f36/compare?selectedSessions=56647b7a-9cb5-4118-8ac3-b89c51e744c0\n", "\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["0it [00:00, ?it/s]Error running evaluator <DynamicRunEvaluator evaluate_no_assumptions> on run 81e5ead6-3e28-4568-ab56-ead2a52ffa22: RateLimitError(\"Error code: 429 - {'error': {'message': '您已达到请求数限制：1分钟内最多请求10次 (request id: 20250822114809353638241ZUhXWwxd) (request id: 20250822114809165488452w9vFXyiE)', 'type': 'openai_error', 'param': '', 'code': '<nil>'}}\")\n", "Traceback (most recent call last):\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/_runner.py\", line 1620, in _run_evaluators\n", "    evaluator_response = evaluator.evaluate_run(  # type: ignore[call-arg]\n", "                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/evaluator.py\", line 351, in evaluate_run\n", "    result = self.func(\n", "             ^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/evaluator.py\", line 777, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/var/folders/pm/hwrmmkn90ms1l7m9byllqqnh0000gn/T/ipykernel_49771/1938646463.py\", line 38, in evaluate_no_assumptions\n", "    response = structured_output_model.invoke([\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/runnables/base.py\", line 3047, in invoke\n", "    input_ = context.run(step.invoke, input_, config, **kwargs)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/runnables/base.py\", line 5441, in invoke\n", "    return self.bound.invoke(\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 383, in invoke\n", "    self.generate_prompt(\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 1006, in generate_prompt\n", "    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 825, in generate\n", "    self._generate_with_cache(\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 1072, in _generate_with_cache\n", "    result = self._generate(\n", "             ^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_openai/chat_models/base.py\", line 1153, in _generate\n", "    response = self.root_client.beta.chat.completions.parse(**payload)\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/openai/resources/chat/completions/completions.py\", line 183, in parse\n", "    return self._post(\n", "           ^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/openai/_base_client.py\", line 1259, in post\n", "    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n", "                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/openai/_base_client.py\", line 1047, in request\n", "    raise self._make_status_error_from_response(err.response) from None\n", "openai.RateLimitError: Error code: 429 - {'error': {'message': '您已达到请求数限制：1分钟内最多请求10次 (request id: 20250822114809353638241ZUhXWwxd) (request id: 20250822114809165488452w9vFXyiE)', 'type': 'openai_error', 'param': '', 'code': '<nil>'}}\n", "1it [00:08,  8.36s/it]Error running target function: Error code: 429 - {'error': {'message': '您已达到请求数限制：1分钟内最多请求10次 (request id: 20250822114811475069828JcRCDip3) (request id: 202508221148111246003548RaGZ9cK)', 'type': 'openai_error', 'param': '', 'code': '<nil>'}}\n", "Traceback (most recent call last):\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/_runner.py\", line 1924, in _forward\n", "    fn(*args, langsmith_extra=langsmith_extra)\n", "  File \"/var/folders/pm/hwrmmkn90ms1l7m9byllqqnh0000gn/T/ipykernel_49771/3900603134.py\", line 5, in target_func\n", "    return scope.invoke(inputs, config=config)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langgraph/pregel/main.py\", line 3026, in invoke\n", "    for chunk in self.stream(\n", "                 ^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langgraph/pregel/main.py\", line 2647, in stream\n", "    for _ in runner.tick(\n", "             ^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langgraph/pregel/_runner.py\", line 162, in tick\n", "    run_with_retry(\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langgraph/pregel/_retry.py\", line 42, in run_with_retry\n", "    return task.proc.invoke(task.input, config)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langgraph/_internal/_runnable.py\", line 657, in invoke\n", "    input = context.run(step.invoke, input, config, **kwargs)\n", "            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langgraph/_internal/_runnable.py\", line 401, in invoke\n", "    ret = self.func(*args, **kwargs)\n", "          ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Desktop/projects/github/deep_research_from_scratch/notebooks/deep_research_from_scratch/research_agent_scope.py\", line 47, in clarify_with_user\n", "    response = structured_output_model.invoke([\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/runnables/base.py\", line 3047, in invoke\n", "    input_ = context.run(step.invoke, input_, config, **kwargs)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/runnables/base.py\", line 5441, in invoke\n", "    return self.bound.invoke(\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 383, in invoke\n", "    self.generate_prompt(\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 1006, in generate_prompt\n", "    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 825, in generate\n", "    self._generate_with_cache(\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_core/language_models/chat_models.py\", line 1072, in _generate_with_cache\n", "    result = self._generate(\n", "             ^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langchain_openai/chat_models/base.py\", line 1153, in _generate\n", "    response = self.root_client.beta.chat.completions.parse(**payload)\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/openai/resources/chat/completions/completions.py\", line 183, in parse\n", "    return self._post(\n", "           ^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/openai/_base_client.py\", line 1259, in post\n", "    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n", "                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/openai/_base_client.py\", line 1047, in request\n", "    raise self._make_status_error_from_response(err.response) from None\n", "openai.RateLimitError: Error code: 429 - {'error': {'message': '您已达到请求数限制：1分钟内最多请求10次 (request id: 20250822114811475069828JcRCDip3) (request id: 202508221148111246003548RaGZ9cK)', 'type': 'openai_error', 'param': '', 'code': '<nil>'}}\n", "During task with name 'clarify_with_user' and id '32183d7f-fce5-4781-586f-ffe0fb6e55ff'\n", "Error running evaluator <DynamicRunEvaluator evaluate_success_criteria> on run 13a3c955-d3f5-405c-97bd-17c03db56c27: KeyError('research_brief')\n", "Traceback (most recent call last):\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/_runner.py\", line 1620, in _run_evaluators\n", "    evaluator_response = evaluator.evaluate_run(  # type: ignore[call-arg]\n", "                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/evaluator.py\", line 351, in evaluate_run\n", "    result = self.func(\n", "             ^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/evaluator.py\", line 777, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/var/folders/pm/hwrmmkn90ms1l7m9byllqqnh0000gn/T/ipykernel_49771/3871616160.py\", line 37, in evaluate_success_criteria\n", "    research_brief = outputs[\"research_brief\"]\n", "                     ~~~~~~~^^^^^^^^^^^^^^^^^^\n", "KeyError: 'research_brief'\n", "Error running evaluator <DynamicRunEvaluator evaluate_no_assumptions> on run 13a3c955-d3f5-405c-97bd-17c03db56c27: KeyError('research_brief')\n", "Traceback (most recent call last):\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/_runner.py\", line 1620, in _run_evaluators\n", "    evaluator_response = evaluator.evaluate_run(  # type: ignore[call-arg]\n", "                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/evaluator.py\", line 351, in evaluate_run\n", "    result = self.func(\n", "             ^^^^^^^^^^\n", "  File \"/opt/miniconda3/envs/langgraph/lib/python3.12/site-packages/langsmith/evaluation/evaluator.py\", line 777, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/var/folders/pm/hwrmmkn90ms1l7m9byllqqnh0000gn/T/ipykernel_49771/1938646463.py\", line 32, in evaluate_no_assumptions\n", "    research_brief = outputs[\"research_brief\"]\n", "                     ~~~~~~~^^^^^^^^^^^^^^^^^^\n", "KeyError: 'research_brief'\n", "2it [00:11,  5.61s/it]\n"]}, {"data": {"text/html": ["<ExperimentResults Deep Research Scoping-6bf0b71c>"], "text/plain": ["<ExperimentResults Deep Research Scoping-6bf0b71c>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["import uuid\n", "\n", "def target_func(inputs: dict):\n", "    config = {\"configurable\": {\"thread_id\": uuid.uuid4()}}\n", "    return scope.invoke(inputs, config=config)\n", "\n", "langsmith_client.evaluate(\n", "    target_func,\n", "    data=dataset_name,\n", "    evaluators=[evaluate_success_criteria, evaluate_no_assumptions],\n", "    experiment_prefix=\"Deep Research Scoping\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can click the above link to take a look at the results! \n", "\n", "Why perform evals like this?\n", "\n", "* Ensure that individual steps in your application are doing what you expect.\n", "* With tracing to Lang<PERSON><PERSON>, you also get a sense of how long each step takes as well as the cost.\n", "* You can always try out cheaper, faster models to see if they can get the job done.\n", "\n", "If your agent makes mistakes, you can tune the prompts that the agent is provided to try and improve performance as well!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langgraph", "language": "python", "name": "langgraph"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}